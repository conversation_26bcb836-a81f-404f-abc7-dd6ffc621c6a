import { motion } from 'framer-motion'
import { 
  Target, 
  Eye, 
  Heart, 
  Users, 
  Globe, 
  Award,
  Leaf,
  Truck,
  CheckCircle
} from 'lucide-react'

const About = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const values = [
    {
      icon: <Leaf className="h-8 w-8" />,
      title: "Sustainability",
      description: "Committed to eco-friendly practices that protect our environment for future generations."
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Quality",
      description: "Maintaining the highest standards in all our products with rigorous quality control."
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Tradition",
      description: "Honoring ancient Vedic wisdom while embracing modern sustainable practices."
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "Global Reach",
      description: "Bringing premium Indian cow-based products to customers worldwide."
    }
  ]

  const process = [
    {
      step: "01",
      title: "Sourcing",
      description: "We source from certified organic farms with healthy, well-cared-for cows."
    },
    {
      step: "02",
      title: "Processing",
      description: "Traditional methods combined with modern hygiene standards for optimal quality."
    },
    {
      step: "03",
      title: "Quality Control",
      description: "Rigorous testing and certification to ensure export-quality standards."
    },
    {
      step: "04",
      title: "Packaging",
      description: "Eco-friendly packaging that preserves product quality during transport."
    },
    {
      step: "05",
      title: "Shipping",
      description: "Global logistics network ensuring timely delivery worldwide."
    }
  ]

  const stats = [
    { number: "2018", label: "Founded" },
    { number: "500+", label: "Happy Customers" },
    { number: "50+", label: "Countries Served" },
    { number: "10,000+", label: "Tons Exported" }
  ]

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-cream py-20">
        <div className="container-custom">
          <motion.div
            className="text-center max-w-4xl mx-auto space-y-6"
            {...fadeInUp}
          >
            <h1 className="text-5xl lg:text-6xl font-heading font-bold text-primary-900">
              About Gauveda Global
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed">
              Bridging ancient wisdom with modern sustainability to bring you 
              the finest cow-based products from the heart of India to the world.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Company Story */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl font-heading font-bold text-primary-900">
                Our Story
              </h2>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  Founded in 2018, Gauveda Global emerged from a deep respect for traditional 
                  Indian agricultural practices and a vision to share these time-tested solutions 
                  with the modern world. Our journey began in the rural heartlands of India, 
                  where cow-based products have been integral to sustainable farming for millennia.
                </p>
                <p>
                  We recognized the growing global demand for eco-friendly, organic alternatives 
                  to chemical fertilizers and synthetic fuels. This inspired us to create a 
                  bridge between traditional wisdom and contemporary needs, ensuring that 
                  ancient practices could benefit modern sustainable living.
                </p>
                <p>
                  Today, we proudly serve customers across 50+ countries, maintaining our 
                  commitment to quality, sustainability, and the welfare of both farmers 
                  and the environment.
                </p>
              </div>
            </motion.div>

            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <img
                src="/api/placeholder/500/400"
                alt="Traditional farming with cows"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute -bottom-4 -left-4 w-full h-full bg-gold-200 rounded-2xl -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <motion.div
              className="bg-white p-8 rounded-2xl shadow-lg"
              {...fadeInUp}
            >
              <div className="flex items-center mb-6">
                <div className="bg-primary-800 p-3 rounded-full mr-4">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-heading font-bold text-primary-900">
                  Our Mission
                </h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To provide premium, eco-friendly cow-based products that promote 
                sustainable agriculture and living while honoring traditional wisdom. 
                We strive to create a positive impact on farmers' livelihoods, 
                environmental health, and global food security through our 
                commitment to quality and sustainability.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-8 rounded-2xl shadow-lg"
              {...fadeInUp}
              transition={{ delay: 0.2 }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-gold-600 p-3 rounded-full mr-4">
                  <Eye className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-heading font-bold text-primary-900">
                  Our Vision
                </h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To become the world's leading provider of sustainable cow-based 
                products, fostering a global community that values traditional 
                wisdom, environmental stewardship, and ethical farming practices. 
                We envision a future where our products contribute to a healthier 
                planet and more sustainable agricultural systems worldwide.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              These fundamental principles guide everything we do and shape 
              our commitment to excellence and sustainability.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {values.map((value, index) => (
              <motion.div
                key={index}
                className="text-center space-y-4 p-6 rounded-xl hover:bg-cream transition-colors duration-300"
                variants={fadeInUp}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                  {value.icon}
                </div>
                <h3 className="text-xl font-heading font-semibold text-primary-900">
                  {value.title}
                </h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Stats */}
      <section className="section-padding bg-primary-800 text-white">
        <div className="container-custom">
          <motion.div
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                variants={fadeInUp}
              >
                <div className="text-4xl lg:text-5xl font-bold text-gold-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-green-200 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Our Process
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              From sourcing to delivery, we maintain the highest standards
              at every step to ensure premium quality products.
            </p>
          </motion.div>

          <motion.div
            className="space-y-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {process.map((step, index) => (
              <motion.div
                key={index}
                className={`flex flex-col lg:flex-row items-center gap-8 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
                variants={fadeInUp}
              >
                <div className="flex-1 bg-white p-8 rounded-2xl shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="bg-primary-800 text-white text-2xl font-bold w-12 h-12 rounded-full flex items-center justify-center mr-4">
                      {step.step}
                    </div>
                    <h3 className="text-2xl font-heading font-bold text-primary-900">
                      {step.title}
                    </h3>
                  </div>
                  <p className="text-gray-700 leading-relaxed">{step.description}</p>
                </div>
                <div className="flex-1">
                  <img
                    src={`/api/placeholder/400/300`}
                    alt={step.title}
                    className="rounded-2xl shadow-lg w-full"
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Our Commitment
            </h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              At Gauveda Global, we're more than just a supplier – we're partners
              in building a sustainable future. Our team is dedicated to maintaining
              the highest standards while fostering long-term relationships with
              farmers, customers, and communities worldwide.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <motion.div
              className="text-center space-y-4 p-6"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Users className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Farmer Partnerships
              </h3>
              <p className="text-gray-600">
                We work directly with local farmers, ensuring fair prices and
                sustainable farming practices that benefit entire communities.
              </p>
            </motion.div>

            <motion.div
              className="text-center space-y-4 p-6"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <CheckCircle className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Quality Assurance
              </h3>
              <p className="text-gray-600">
                Every product undergoes rigorous testing and quality control
                to meet international standards and customer expectations.
              </p>
            </motion.div>

            <motion.div
              className="text-center space-y-4 p-6"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Truck className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Global Logistics
              </h3>
              <p className="text-gray-600">
                Our efficient logistics network ensures timely delivery
                worldwide while maintaining product integrity and freshness.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default About
