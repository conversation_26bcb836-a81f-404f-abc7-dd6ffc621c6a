{"hash": "b82d1df3", "configHash": "e2b008fc", "lockfileHash": "403bb022", "browserHash": "2a333a28", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "3ee7409f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "62c42171", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1e7fc237", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ffd0a3a5", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "98bb7be9", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "0879422d", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "73dfa7eb", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "cb09e49d", "needsInterop": false}}, "chunks": {"chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-ZTDXEEBU": {"file": "chunk-ZTDXEEBU.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}