import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  Truck, 
  Globe, 
  Award,
  Download,
  CheckCircle,
  Users,
  Clock,
  Shield,
  Calculator
} from 'lucide-react'

const BulkBuyers = () => {
  const [formData, setFormData] = useState({
    companyName: '',
    contactPerson: '',
    email: '',
    phone: '',
    country: '',
    productInterest: '',
    quantity: '',
    frequency: '',
    message: ''
  })

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
    alert('Thank you for your inquiry! We will contact you within 24 hours.')
  }

  const benefits = [
    {
      icon: <Package className="h-8 w-8" />,
      title: "Bulk Pricing",
      description: "Competitive wholesale rates for large quantity orders"
    },
    {
      icon: <Truck className="h-8 w-8" />,
      title: "Global Shipping",
      description: "Worldwide delivery with proper documentation and tracking"
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Quality Assurance",
      description: "Consistent quality with international certifications"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Dedicated Support",
      description: "Personal account manager for all your business needs"
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: "Timely Delivery",
      description: "Reliable supply chain with on-time delivery guarantee"
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Trade Assurance",
      description: "Secure transactions with payment protection"
    }
  ]

  const specifications = [
    {
      product: "Cow Dung Manure",
      moq: "5 Tons",
      packaging: "25kg/50kg bags",
      leadTime: "15-20 days"
    },
    {
      product: "Cow Dung Cakes",
      moq: "1000 pieces",
      packaging: "Bundles of 50",
      leadTime: "10-15 days"
    },
    {
      product: "Cow Dung Logs",
      moq: "2 Tons",
      packaging: "20kg bundles",
      leadTime: "12-18 days"
    },
    {
      product: "Bio-Fuel Pellets",
      moq: "3 Tons",
      packaging: "25kg bags",
      leadTime: "15-20 days"
    }
  ]

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-cream py-20">
        <div className="container-custom">
          <motion.div
            className="text-center max-w-4xl mx-auto space-y-6"
            {...fadeInUp}
          >
            <h1 className="text-5xl lg:text-6xl font-heading font-bold text-primary-900">
              For Bulk Buyers
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed">
              Partner with us for large-scale supply of premium cow-based products. 
              We offer competitive pricing, reliable delivery, and exceptional service 
              for distributors, exporters, and large-scale buyers worldwide.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/catalog.pdf" 
                className="btn-primary inline-flex items-center"
                download
              >
                <Download className="mr-2 h-5 w-5" />
                Download Catalog
              </a>
              <a href="#quote-form" className="btn-outline">
                Get Quote Now
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Why Choose Us for Bulk Orders?
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              We understand the unique needs of bulk buyers and provide 
              tailored solutions to meet your business requirements.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="text-center space-y-4 p-6 rounded-xl hover:bg-cream transition-colors duration-300"
                variants={fadeInUp}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-heading font-semibold text-primary-900">
                  {benefit.title}
                </h3>
                <p className="text-gray-600">{benefit.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Product Specifications */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Product Specifications & MOQ
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Minimum order quantities and specifications for our bulk products. 
              Custom packaging and quantities available on request.
            </p>
          </motion.div>

          <motion.div
            className="overflow-x-auto"
            {...fadeInUp}
          >
            <table className="w-full bg-white rounded-xl shadow-lg overflow-hidden">
              <thead className="bg-primary-800 text-white">
                <tr>
                  <th className="px-6 py-4 text-left font-heading font-semibold">Product</th>
                  <th className="px-6 py-4 text-left font-heading font-semibold">MOQ</th>
                  <th className="px-6 py-4 text-left font-heading font-semibold">Packaging</th>
                  <th className="px-6 py-4 text-left font-heading font-semibold">Lead Time</th>
                </tr>
              </thead>
              <tbody>
                {specifications.map((spec, index) => (
                  <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-6 py-4 font-medium text-primary-900">{spec.product}</td>
                    <td className="px-6 py-4 text-gray-700">{spec.moq}</td>
                    <td className="px-6 py-4 text-gray-700">{spec.packaging}</td>
                    <td className="px-6 py-4 text-gray-700">{spec.leadTime}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </motion.div>

          <motion.div
            className="mt-8 p-6 bg-gold-50 rounded-xl border border-gold-200"
            {...fadeInUp}
          >
            <div className="flex items-start space-x-3">
              <Calculator className="h-6 w-6 text-gold-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-heading font-semibold text-gold-800 mb-2">
                  Custom Requirements?
                </h3>
                <p className="text-gold-700">
                  We can accommodate custom packaging sizes, mixed containers, 
                  and special requirements. Contact us with your specific needs 
                  for a personalized quote.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Quote Form */}
      <section id="quote-form" className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl font-heading font-bold text-primary-900">
                Request a Quote
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                Fill out the form below and our team will get back to you within 
                24 hours with a detailed quote tailored to your requirements.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Free consultation and quote</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">24-hour response guarantee</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Competitive bulk pricing</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Global shipping solutions</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-cream p-8 rounded-2xl"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company Name *
                    </label>
                    <input
                      type="text"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Person *
                    </label>
                    <input
                      type="text"
                      name="contactPerson"
                      value={formData.contactPerson}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Country *
                  </label>
                  <input
                    type="text"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product Interest *
                    </label>
                    <select
                      name="productInterest"
                      value={formData.productInterest}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="">Select Product</option>
                      <option value="manure">Cow Dung Manure</option>
                      <option value="cakes">Cow Dung Cakes</option>
                      <option value="logs">Cow Dung Logs</option>
                      <option value="pellets">Bio-Fuel Pellets</option>
                      <option value="mixed">Mixed Products</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity Required
                    </label>
                    <input
                      type="text"
                      name="quantity"
                      value={formData.quantity}
                      onChange={handleInputChange}
                      placeholder="e.g., 10 tons, 5000 pieces"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Order Frequency
                  </label>
                  <select
                    name="frequency"
                    value={formData.frequency}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select Frequency</option>
                    <option value="one-time">One-time Order</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="bi-annual">Bi-Annual</option>
                    <option value="annual">Annual</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Requirements
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Please specify any special requirements, packaging preferences, or additional information..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full btn-primary"
                >
                  Submit Quote Request
                </button>
              </form>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default BulkBuyers
