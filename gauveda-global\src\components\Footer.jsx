import { Link } from 'react-router-dom'
import { Leaf, Mail, Phone, MapPin, Facebook, Instagram, Twitter, Linkedin } from 'lucide-react'

const Footer = () => {
  return (
    <footer className="bg-primary-900 text-white">
      <div className="container-custom section-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="bg-gold-600 p-2 rounded-full">
                <Leaf className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-heading font-bold">Gauveda Global</h3>
                <p className="text-sm text-green-200">Premium Cow-Based Products</p>
              </div>
            </div>
            <p className="text-green-100 text-sm leading-relaxed">
              From the Land of Vedas to the World. We provide premium, eco-friendly 
              cow-based products for sustainable living and export quality standards.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-green-200 hover:text-gold-400 transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-green-200 hover:text-gold-400 transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-green-200 hover:text-gold-400 transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-green-200 hover:text-gold-400 transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-heading font-semibold mb-4 text-gold-400">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-green-100 hover:text-gold-400 transition-colors text-sm">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-green-100 hover:text-gold-400 transition-colors text-sm">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/products" className="text-green-100 hover:text-gold-400 transition-colors text-sm">
                  Products
                </Link>
              </li>
              <li>
                <Link to="/bulk-buyers" className="text-green-100 hover:text-gold-400 transition-colors text-sm">
                  For Bulk Buyers
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-green-100 hover:text-gold-400 transition-colors text-sm">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Products */}
          <div>
            <h4 className="text-lg font-heading font-semibold mb-4 text-gold-400">Our Products</h4>
            <ul className="space-y-2">
              <li className="text-green-100 text-sm">Cow Dung Manure</li>
              <li className="text-green-100 text-sm">Cow Dung Cakes</li>
              <li className="text-green-100 text-sm">Cow Dung Logs</li>
              <li className="text-green-100 text-sm">Organic Fertilizers</li>
              <li className="text-green-100 text-sm">Bio Fuel Products</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-heading font-semibold mb-4 text-gold-400">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-gold-400 mt-1 flex-shrink-0" />
                <p className="text-green-100 text-sm">
                  123 Rural Road, Village Name,<br />
                  District, State - 123456, India
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-gold-400 flex-shrink-0" />
                <p className="text-green-100 text-sm">+91 98765 43210</p>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gold-400 flex-shrink-0" />
                <p className="text-green-100 text-sm"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-green-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-green-200 text-sm">
            © 2024 Gauveda Global. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-green-200 hover:text-gold-400 text-sm transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="text-green-200 hover:text-gold-400 text-sm transition-colors">
              Terms of Service
            </a>
            <a href="#" className="text-green-200 hover:text-gold-400 text-sm transition-colors">
              Certifications
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
