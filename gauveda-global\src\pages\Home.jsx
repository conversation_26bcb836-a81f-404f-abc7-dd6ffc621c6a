import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { 
  Leaf, 
  Globe, 
  Award, 
  Truck, 
  Users, 
  Star,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const Home = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const products = [
    {
      name: "Cow Dung Manure",
      description: "Premium organic fertilizer for sustainable farming",
      image: "/api/placeholder/300/200"
    },
    {
      name: "Cow Dung Cakes",
      description: "Traditional fuel cakes for eco-friendly cooking",
      image: "/api/placeholder/300/200"
    },
    {
      name: "Cow Dung Logs",
      description: "Compressed logs for efficient burning",
      image: "/api/placeholder/300/200"
    }
  ]

  const features = [
    {
      icon: <Leaf className="h-8 w-8" />,
      title: "100% Natural",
      description: "Pure cow-based products without any chemicals"
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Export Quality",
      description: "International standards and certifications"
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "Eco-Friendly",
      description: "Sustainable and environmentally conscious"
    },
    {
      icon: <Truck className="h-8 w-8" />,
      title: "Global Shipping",
      description: "Worldwide delivery with proper packaging"
    }
  ]

  const testimonials = [
    {
      name: "Rajesh Kumar",
      role: "Organic Farmer",
      content: "The cow dung manure has significantly improved my crop yield. Excellent quality!",
      rating: 5
    },
    {
      name: "Sarah Johnson",
      role: "Export Partner",
      content: "Reliable supplier with consistent quality. Great for international markets.",
      rating: 5
    },
    {
      name: "Amit Patel",
      role: "Bulk Buyer",
      content: "Professional service and timely delivery. Highly recommended for bulk orders.",
      rating: 5
    }
  ]

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-cream min-h-screen flex items-center">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-heading font-bold text-primary-900 leading-tight">
                  From the Land of{' '}
                  <span className="text-gold-600">Vedas</span>{' '}
                  to the World
                </h1>
                <p className="text-xl text-gray-700 leading-relaxed">
                  Premium Cow-Based Products for a Sustainable Future. 
                  Export quality organic solutions for modern living.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/products" className="btn-primary">
                  Explore Products
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link to="/bulk-buyers" className="btn-outline">
                  For Bulk Buyers
                </Link>
              </div>

              <div className="flex items-center space-x-8 pt-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-800">500+</div>
                  <div className="text-sm text-gray-600">Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-800">50+</div>
                  <div className="text-sm text-gray-600">Countries Served</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-800">100%</div>
                  <div className="text-sm text-gray-600">Natural Products</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="relative z-10">
                <img
                  src="/api/placeholder/600/400"
                  alt="Cow dung products in natural setting"
                  className="rounded-2xl shadow-2xl"
                />
              </div>
              <div className="absolute -top-4 -right-4 w-full h-full bg-gold-200 rounded-2xl -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center max-w-3xl mx-auto space-y-6"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900">
              About Gauveda Global
            </h2>
            <p className="text-lg text-gray-700 leading-relaxed">
              We are committed to bringing you the finest cow-based products that honor 
              traditional wisdom while meeting modern sustainability standards. Our mission 
              is to provide eco-friendly solutions that benefit both farmers and the environment.
            </p>
            <Link to="/about" className="btn-outline inline-flex items-center">
              Learn More About Us
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Products Highlights */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Our Premium Products
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Discover our range of high-quality, eco-friendly cow-based products 
              perfect for sustainable living and farming.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {products.map((product, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl shadow-lg overflow-hidden card-hover"
                variants={fadeInUp}
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-heading font-semibold text-primary-900 mb-2">
                    {product.name}
                  </h3>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  <button className="text-primary-800 font-semibold hover:text-primary-600 transition-colors">
                    Learn More →
                  </button>
                </div>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            className="text-center mt-12"
            {...fadeInUp}
          >
            <Link to="/products" className="btn-primary">
              View All Products
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Why Choose Gauveda Global?
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              We combine traditional wisdom with modern quality standards to deliver
              the best cow-based products for your needs.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="text-center space-y-4 p-6 rounded-xl bg-cream hover:bg-primary-50 transition-colors duration-300"
                variants={fadeInUp}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-heading font-semibold text-primary-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Bulk Buyers CTA */}
      <section className="section-padding bg-primary-800 text-white">
        <div className="container-custom">
          <motion.div
            className="text-center space-y-8"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold">
              Looking for Bulk Orders?
            </h2>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              We specialize in large-scale supply with competitive pricing,
              quality assurance, and global shipping capabilities. Perfect for
              distributors, exporters, and large-scale farmers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/bulk-buyers" className="btn-secondary">
                Enquire Now
              </Link>
              <a
                href="/catalog.pdf"
                className="btn-outline border-white text-white hover:bg-white hover:text-primary-800"
                download
              >
                Download Catalog
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied customers
              have to say about our products and services.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg"
                variants={fadeInUp}
              >
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-gold-500 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.content}"</p>
                <div>
                  <div className="font-semibold text-primary-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Home
