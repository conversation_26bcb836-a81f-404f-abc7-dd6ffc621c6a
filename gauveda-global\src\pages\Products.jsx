import { useState } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { 
  Package, 
  Leaf, 
  Flame, 
  Truck, 
  Award,
  ArrowRight,
  Filter,
  Grid,
  List
} from 'lucide-react'

const Products = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState('grid')

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const categories = [
    { id: 'all', name: 'All Products', icon: <Package className="h-5 w-5" /> },
    { id: 'manure', name: 'Manure', icon: <Leaf className="h-5 w-5" /> },
    { id: 'fuel', name: 'Fuel Products', icon: <Flame className="h-5 w-5" /> },
    { id: 'organic', name: 'Organic', icon: <Award className="h-5 w-5" /> }
  ]

  const products = [
    {
      id: 1,
      name: "Premium Cow Dung Manure",
      category: "manure",
      description: "High-quality organic fertilizer perfect for sustainable farming and gardening.",
      features: ["100% Organic", "Rich in Nutrients", "Improves Soil Health", "Eco-Friendly"],
      image: "/api/placeholder/400/300",
      packaging: "25kg, 50kg bags",
      uses: "Agriculture, Gardening, Organic Farming",
      benefits: "Enhances soil fertility, improves crop yield, sustainable farming solution"
    },
    {
      id: 2,
      name: "Traditional Cow Dung Cakes",
      category: "fuel",
      description: "Handcrafted fuel cakes for traditional cooking and heating purposes.",
      features: ["Smokeless Burning", "Long Lasting", "Traditional Method", "Carbon Neutral"],
      image: "/api/placeholder/400/300",
      packaging: "Bundles of 10, 20, 50 pieces",
      uses: "Cooking, Heating, Religious Ceremonies",
      benefits: "Clean burning, cost-effective, traditional fuel source"
    },
    {
      id: 3,
      name: "Compressed Cow Dung Logs",
      category: "fuel",
      description: "Modern compressed logs for efficient burning and heating applications.",
      features: ["High Calorific Value", "Uniform Shape", "Easy Storage", "Consistent Quality"],
      image: "/api/placeholder/400/300",
      packaging: "10kg, 20kg bundles",
      uses: "Industrial Heating, Biomass Fuel, Cooking",
      benefits: "Efficient burning, easy handling, renewable energy source"
    },
    {
      id: 4,
      name: "Organic Compost Mix",
      category: "organic",
      description: "Premium compost blend with cow dung for enhanced plant growth.",
      features: ["Nutrient Rich", "pH Balanced", "Pathogen Free", "Ready to Use"],
      image: "/api/placeholder/400/300",
      packaging: "10kg, 25kg bags",
      uses: "Home Gardening, Potting Mix, Landscaping",
      benefits: "Promotes healthy plant growth, improves soil structure"
    },
    {
      id: 5,
      name: "Dried Cow Dung Powder",
      category: "manure",
      description: "Fine powder form for easy application and mixing with other fertilizers.",
      features: ["Fine Texture", "Easy Application", "Quick Absorption", "Concentrated"],
      image: "/api/placeholder/400/300",
      packaging: "5kg, 10kg, 25kg bags",
      uses: "Liquid Fertilizer Base, Soil Amendment, Organic Farming",
      benefits: "Easy to use, quick nutrient release, versatile application"
    },
    {
      id: 6,
      name: "Bio-Fuel Pellets",
      category: "fuel",
      description: "Modern pelletized form for industrial and domestic heating applications.",
      features: ["High Density", "Low Moisture", "Consistent Size", "Clean Burning"],
      image: "/api/placeholder/400/300",
      packaging: "15kg, 25kg bags",
      uses: "Pellet Stoves, Industrial Boilers, Heating Systems",
      benefits: "High efficiency, low emissions, renewable energy"
    }
  ]

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory)

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-cream py-20">
        <div className="container-custom">
          <motion.div
            className="text-center max-w-4xl mx-auto space-y-6"
            {...fadeInUp}
          >
            <h1 className="text-5xl lg:text-6xl font-heading font-bold text-primary-900">
              Our Premium Products
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed">
              Discover our comprehensive range of high-quality, eco-friendly cow-based 
              products designed for sustainable living and farming.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter and View Controls */}
      <section className="py-8 bg-white border-b">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary-800 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-primary-100'
                  }`}
                >
                  {category.icon}
                  <span>{category.name}</span>
                </button>
              ))}
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-4">
              <span className="text-gray-600 font-medium">View:</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-white text-primary-800 shadow-sm'
                      : 'text-gray-600 hover:text-primary-800'
                  }`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white text-primary-800 shadow-sm'
                      : 'text-gray-600 hover:text-primary-800'
                  }`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid/List */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className={`grid gap-8 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {filteredProducts.map((product) => (
              <motion.div
                key={product.id}
                className={`bg-white rounded-xl shadow-lg overflow-hidden card-hover ${
                  viewMode === 'list' ? 'flex flex-col lg:flex-row' : ''
                }`}
                variants={fadeInUp}
              >
                <div className={`${viewMode === 'list' ? 'lg:w-1/3' : ''}`}>
                  <img
                    src={product.image}
                    alt={product.name}
                    className={`w-full object-cover ${
                      viewMode === 'list' ? 'h-64 lg:h-full' : 'h-48'
                    }`}
                  />
                </div>
                
                <div className={`p-6 ${viewMode === 'list' ? 'lg:w-2/3' : ''}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className="inline-block bg-primary-100 text-primary-800 text-xs font-semibold px-2 py-1 rounded-full">
                      {categories.find(cat => cat.id === product.category)?.name}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-heading font-semibold text-primary-900 mb-3">
                    {product.name}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {product.description}
                  </p>

                  {viewMode === 'list' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="font-semibold text-primary-900 mb-2">Packaging:</h4>
                        <p className="text-sm text-gray-600">{product.packaging}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-primary-900 mb-2">Uses:</h4>
                        <p className="text-sm text-gray-600">{product.uses}</p>
                      </div>
                    </div>
                  )}

                  <div className="mb-4">
                    <h4 className="font-semibold text-primary-900 mb-2">Key Features:</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.features.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Link
                      to="/bulk-buyers"
                      className="btn-primary flex-1 text-center"
                    >
                      Get Quote
                    </Link>
                    <button className="btn-outline flex-1">
                      Learn More
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary-800 text-white">
        <div className="container-custom">
          <motion.div
            className="text-center space-y-8"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold">
              Need Custom Solutions?
            </h2>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              We offer customized packaging, bulk quantities, and specialized
              products to meet your specific requirements. Contact us for
              personalized solutions and competitive pricing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/bulk-buyers" className="btn-secondary">
                Request Custom Quote
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link to="/contact" className="btn-outline border-white text-white hover:bg-white hover:text-primary-800">
                Contact Our Team
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Why Choose Our Products?
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Every product is crafted with care, maintaining the highest
              standards of quality and sustainability.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <motion.div
              className="text-center space-y-4"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Award className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Premium Quality
              </h3>
              <p className="text-gray-600">
                Rigorous quality control ensures every product meets international standards.
              </p>
            </motion.div>

            <motion.div
              className="text-center space-y-4"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Leaf className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                100% Organic
              </h3>
              <p className="text-gray-600">
                All products are naturally sourced without any chemical additives.
              </p>
            </motion.div>

            <motion.div
              className="text-center space-y-4"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Package className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Secure Packaging
              </h3>
              <p className="text-gray-600">
                Eco-friendly packaging that preserves quality during transport.
              </p>
            </motion.div>

            <motion.div
              className="text-center space-y-4"
              variants={fadeInUp}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-800 text-white rounded-full">
                <Truck className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-heading font-semibold text-primary-900">
                Global Delivery
              </h3>
              <p className="text-gray-600">
                Worldwide shipping with tracking and insurance coverage.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Products
