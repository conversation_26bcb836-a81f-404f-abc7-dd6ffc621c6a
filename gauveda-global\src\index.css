@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap');

:root {
  /* Color Variables */
  --primary-50: #e8f5e8;
  --primary-100: #c8e6c8;
  --primary-200: #a5d6a5;
  --primary-300: #81c784;
  --primary-400: #66bb6a;
  --primary-500: #4caf50;
  --primary-600: #43a047;
  --primary-700: #388e3c;
  --primary-800: #2e7d32;
  --primary-900: #1b5e20;

  --earth-50: #f3f0ec;
  --earth-100: #e0d9d0;
  --earth-200: #ccbfb1;
  --earth-300: #b8a592;
  --earth-400: #a8917a;
  --earth-500: #987d62;
  --earth-600: #8b5a2b;
  --earth-700: #7a4f26;
  --earth-800: #694420;
  --earth-900: #583a1b;

  --gold-50: #fffef7;
  --gold-100: #fffbeb;
  --gold-200: #fff3c4;
  --gold-300: #ffec9c;
  --gold-400: #ffe082;
  --gold-500: #ffd54f;
  --gold-600: #ffc107;
  --gold-700: #ffb300;
  --gold-800: #ffa000;
  --gold-900: #ff8f00;

  --cream: #F9F6F1;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--cream);
  line-height: 1.6;
  color: #333;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  line-height: 1.2;
}

/* Utility Classes */
.container-custom {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 2rem;
  }
}

.section-padding {
  padding: 4rem 1rem;
}

@media (min-width: 640px) {
  .section-padding {
    padding: 4rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 4rem 2rem;
  }
}

/* Button Styles */
.btn-primary {
  background-color: var(--primary-800);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--gold-600);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: var(--gold-700);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.btn-outline {
  border: 2px solid var(--primary-800);
  color: var(--primary-800);
  background-color: transparent;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-outline:hover {
  background-color: var(--primary-800);
  color: white;
}

/* Card Styles */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Grid */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .md\\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-6 > * + * {
  margin-left: 1.5rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* Text Utilities */
.text-center {
  text-align: center;
}

.text-white {
  color: white;
}

.text-primary-800 {
  color: var(--primary-800);
}

.text-primary-900 {
  color: var(--primary-900);
}

.text-gold-600 {
  color: var(--gold-600);
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-700 {
  color: #374151;
}

/* Background Utilities */
.bg-white {
  background-color: white;
}

.bg-cream {
  background-color: var(--cream);
}

.bg-primary-50 {
  background-color: var(--primary-50);
}

.bg-primary-800 {
  background-color: var(--primary-800);
}

.bg-primary-900 {
  background-color: var(--primary-900);
}

.bg-gold-600 {
  background-color: var(--gold-600);
}

.bg-amber-50 {
  background-color: #fffbeb;
}

.bg-green-50 {
  background-color: var(--primary-50);
}

.bg-green-800 {
  background-color: var(--primary-800);
}

.bg-green-900 {
  background-color: var(--primary-900);
}

.bg-yellow-400 {
  background-color: var(--gold-400);
}

.bg-yellow-500 {
  background-color: var(--gold-500);
}

/* Padding & Margin */
.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-12 {
  margin-top: 3rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

/* Border & Radius */
.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border-2 {
  border-width: 2px;
}

.border-primary-800 {
  border-color: var(--primary-800);
}

.border-white {
  border-color: white;
}

/* Shadow */
.shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.shadow-xl {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

/* Width & Height */
.w-full {
  width: 100%;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-16 {
  height: 4rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-16 {
  width: 4rem;
}

/* Position */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.sticky {
  position: sticky;
}

.top-0 {
  top: 0;
}

.z-50 {
  z-index: 50;
}

/* Overflow */
.overflow-hidden {
  overflow: hidden;
}

/* Font Sizes */
.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-6xl {
  font-size: 3.75rem;
}

/* Font Weight */
.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Responsive Text */
@media (min-width: 1024px) {
  .lg\\:text-6xl {
    font-size: 3.75rem;
  }
}

/* Transitions */
.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.transition-all {
  transition: all 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Hover States */
.hover\\:bg-primary-700:hover {
  background-color: var(--primary-700);
}

.hover\\:bg-primary-800:hover {
  background-color: var(--primary-800);
}

.hover\\:bg-gold-700:hover {
  background-color: var(--gold-700);
}

.hover\\:text-white:hover {
  color: white;
}

.hover\\:text-primary-800:hover {
  color: var(--primary-800);
}

.hover\\:text-gold-400:hover {
  color: var(--gold-400);
}

.hover\\:shadow-xl:hover {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

.hover\\:scale-105:hover {
  transform: scale(1.05);
}

/* Responsive Utilities */
@media (min-width: 640px) {
  .sm\\:flex-row {
    flex-direction: row;
  }

  .sm\\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .md\\:hidden {
    display: none;
  }

  .md\\:flex {
    display: flex;
  }

  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .md\\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .lg\\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}
