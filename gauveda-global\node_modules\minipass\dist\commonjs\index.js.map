{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,MAAM,IAAI,GACR,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO;IACpC,CAAC,CAAC,OAAO;IACT,CAAC,CAAC;QACE,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;KACb,CAAA;AACP,6CAA0C;AAC1C,8DAAgC;AAChC,6DAAmD;AASnD;;;GAGG;AACI,MAAM,QAAQ,GAAG,CACtB,CAAM,EACsC,EAAE,CAC9C,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,CAAC,CAAC,YAAY,QAAQ;QACpB,CAAC,YAAY,qBAAM;QACnB,IAAA,kBAAU,EAAC,CAAC,CAAC;QACb,IAAA,kBAAU,EAAC,CAAC,CAAC,CAAC,CAAA;AARL,QAAA,QAAQ,YAQH;AAElB;;GAEG;AACI,MAAM,UAAU,GAAG,CAAC,CAAM,EAA0B,EAAE,CAC3D,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,CAAC,YAAY,0BAAY;IACzB,OAAQ,CAAuB,CAAC,IAAI,KAAK,UAAU;IACnD,iEAAiE;IAChE,CAAuB,CAAC,IAAI,KAAK,qBAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAA;AANrD,QAAA,UAAU,cAM2C;AAElE;;GAEG;AACI,MAAM,UAAU,GAAG,CAAC,CAAM,EAA0B,EAAE,CAC3D,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,CAAC,YAAY,0BAAY;IACzB,OAAQ,CAAuB,CAAC,KAAK,KAAK,UAAU;IACpD,OAAQ,CAAuB,CAAC,GAAG,KAAK,UAAU,CAAA;AALvC,QAAA,UAAU,cAK6B;AAEpD,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;AACzB,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACxC,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AAC1C,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AACzC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,0CAA0C;AAC1C,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,0CAA0C;AAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,CAAA;AAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AAErC,MAAM,KAAK,GAAG,CAAC,EAAwB,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACtE,MAAM,OAAO,GAAG,CAAC,EAAwB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAA;AAMlD,MAAM,QAAQ,GAAG,CAAC,EAAO,EAAqB,EAAE,CAC9C,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,WAAW,CAAA;AAEvD,MAAM,iBAAiB,GAAG,CAAC,CAAM,EAAwB,EAAE,CACzD,CAAC,YAAY,WAAW;IACxB,CAAC,CAAC,CAAC,CAAC;QACF,OAAO,CAAC,KAAK,QAAQ;QACrB,CAAC,CAAC,WAAW;QACb,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa;QACpC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAA;AAEtB,MAAM,iBAAiB,GAAG,CAAC,CAAM,EAAwB,EAAE,CACzD,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AAgB9C;;;;GAIG;AACH,MAAM,IAAI;IACR,GAAG,CAAa;IAChB,IAAI,CAAkB;IACtB,IAAI,CAAa;IACjB,OAAO,CAAW;IAClB,YACE,GAAgB,EAChB,IAAuB,EACvB,IAAiB;QAEjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,IAAwB,CAAA;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAA;QAClC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IACrC,CAAC;IACD,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IACjD,CAAC;IACD,8BAA8B;IAC9B,qBAAqB;IACrB,WAAW,CAAC,GAAQ,IAAG,CAAC;IACxB,oBAAoB;IACpB,GAAG;QACD,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;IACpC,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,eAAmB,SAAQ,IAAO;IACtC,MAAM;QACJ,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAClD,KAAK,CAAC,MAAM,EAAE,CAAA;IAChB,CAAC;IACD,YACE,GAAgB,EAChB,IAAuB,EACvB,IAAiB;QAEjB,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAC/C,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnC,CAAC;CACF;AA6ID,MAAM,mBAAmB,GAAG,CAC1B,CAAyB,EACQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA;AAEpD,MAAM,iBAAiB,GAAG,CACxB,CAAyB,EACM,EAAE,CACjC,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAA;AAE1D;;;;;;;;;;GAUG;AACH,MAAa,QAOX,SAAQ,0BAAY;IAGpB,CAAC,OAAO,CAAC,GAAY,KAAK,CAAC;IAC3B,CAAC,MAAM,CAAC,GAAY,KAAK,CAAC;IAC1B,CAAC,KAAK,CAAC,GAAkB,EAAE,CAAC;IAC5B,CAAC,MAAM,CAAC,GAAY,EAAE,CAAC;IACvB,CAAC,UAAU,CAAC,CAAU;IACtB,CAAC,QAAQ,CAAC,CAAwB;IAClC,CAAC,KAAK,CAAC,CAAU;IACjB,CAAC,OAAO,CAAC,CAAY;IACrB,CAAC,GAAG,CAAC,GAAY,KAAK,CAAC;IACvB,CAAC,WAAW,CAAC,GAAY,KAAK,CAAC;IAC/B,CAAC,YAAY,CAAC,GAAY,KAAK,CAAC;IAChC,CAAC,MAAM,CAAC,GAAY,KAAK,CAAC;IAC1B,CAAC,aAAa,CAAC,GAAY,IAAI,CAAC;IAChC,CAAC,YAAY,CAAC,GAAW,CAAC,CAAC;IAC3B,CAAC,SAAS,CAAC,GAAY,KAAK,CAAC;IAC7B,CAAC,MAAM,CAAC,CAAe;IACvB,CAAC,OAAO,CAAC,GAAY,KAAK,CAAC;IAC3B,CAAC,aAAa,CAAC,GAAW,CAAC,CAAC;IAC5B,CAAC,SAAS,CAAC,GAAY,KAAK,CAAA;IAE5B;;OAEG;IACH,QAAQ,GAAY,IAAI,CAAA;IACxB;;OAEG;IACH,QAAQ,GAAY,IAAI,CAAA;IAExB;;;;;OAKG;IACH,YACE,GAAG,IAI+B;QAElC,MAAM,OAAO,GAA4B,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,EAAE,CAA4B,CAAA;QAChC,KAAK,EAAE,CAAA;QACP,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/D,MAAM,IAAI,SAAS,CACjB,kDAAkD,CACnD,CAAA;QACH,CAAC;QACD,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAA;YACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACvB,CAAC;aAAM,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAA;YACjC,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACvB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,CAAC,CAAE,IAAI,mCAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAQ;YAC3C,CAAC,CAAC,IAAI,CAAA;QAER,uDAAuD;QACvD,IAAI,OAAO,IAAI,OAAO,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAClD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACpE,CAAC;QACD,uDAAuD;QACvD,IAAI,OAAO,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAA;QAC1B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAA;YACrB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;YACf,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,IAAI;QACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAuB;QACjC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU,CAAC,GAAG;QAChB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,OAAO,CAAC;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;IACD;;;;;;OAMG;IACH,IAAI,CAAC,OAAO,CAAC,CAAC,CAAU;QACtB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAClC,CAAC;IAED,qDAAqD;IACrD,CAAC,KAAK,CAAC;QACL,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IACD;;;OAGG;IACH,IAAI,OAAO,CAAC,CAAC,IAAG,CAAC;IA0BjB,KAAK,CACH,KAAY,EACZ,QAA2C,EAC3C,EAAe;QAEf,IAAI,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,KAAK,CAAA;QAC/B,IAAI,IAAI,CAAC,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAEjD,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CACP,OAAO,EACP,MAAM,CAAC,MAAM,CACX,IAAI,KAAK,CAAC,gDAAgD,CAAC,EAC3D,EAAE,IAAI,EAAE,sBAAsB,EAAE,CACjC,CACF,CAAA;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,MAAM,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,MAAM,CAAA;QAEhC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAA;QAExC,2DAA2D;QAC3D,+DAA+D;QAC/D,kCAAkC;QAClC,uDAAuD;QACvD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACjD,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,0CAA0C;gBAC1C,KAAK,GAAG,MAAM,CAAC,IAAI,CACjB,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,UAAU,CACjB,CAAA;YACH,CAAC;iBAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpC,0CAA0C;gBAC1C,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5B,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CACb,sDAAsD,CACvD,CAAA;YACH,CAAC;QACH,CAAC;QAED,kDAAkD;QAClD,sDAAsD;QACtD,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrB,oBAAoB;YACpB,qBAAqB;YACrB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAA;YAChE,oBAAoB;YAEpB,IAAI,IAAI,CAAC,OAAO,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAyB,CAAC,CAAA;;gBAC1D,IAAI,CAAC,UAAU,CAAC,CAAC,KAAyB,CAAC,CAAA;YAEhD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAEnD,IAAI,EAAE;gBAAE,EAAE,CAAC,EAAE,CAAC,CAAA;YAEd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;QACtB,CAAC;QAED,gDAAgD;QAChD,+CAA+C;QAC/C,IAAI,CAAE,KAAiC,CAAC,MAAM,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACnD,IAAI,EAAE;gBAAE,EAAE,CAAC,EAAE,CAAC,CAAA;YACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;QACtB,CAAC;QAED,8DAA8D;QAC9D,qDAAqD;QACrD,IACE,OAAO,KAAK,KAAK,QAAQ;YACzB,oDAAoD;YACpD,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,EAC1D,CAAC;YACD,wCAAwC;YACxC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,wCAAwC;YACxC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACpC,CAAC;QAED,iEAAiE;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAA;QAEhE,IAAI,IAAI,CAAC,OAAO,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAyB,CAAC,CAAA;;YAC1D,IAAI,CAAC,UAAU,CAAC,CAAC,KAAyB,CAAC,CAAA;QAEhD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAEnD,IAAI,EAAE;YAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QAEd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,CAAC,CAAiB;QACpB,IAAI,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,IAAI,CAAA;QAChC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;QAEvB,IACE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YACxB,CAAC,KAAK,CAAC;YACP,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAC7B,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;YACtB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC;YAAE,CAAC,GAAG,IAAI,CAAA;QAE9B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,mEAAmE;YACnE,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,GAAG;gBACb,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACb,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,CAAC,CAAC,MAAM,CAAC,MAAM,CACX,IAAI,CAAC,MAAM,CAAa,EACxB,IAAI,CAAC,YAAY,CAAC,CACnB,CAAU;aAChB,CAAA;QACH,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAU,CAAC,CAAA;QAC3D,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;QACtB,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,CAAgB,EAAE,KAAY;QACnC,IAAI,IAAI,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAA;aACpC,CAAC;YACJ,MAAM,CAAC,GAAG,KAAgC,CAAA;YAC1C,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI;gBAAE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAA;iBAChD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAU,CAAA;gBACrC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAU,CAAA;gBAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAU,CAAA;gBACxC,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAU,CAAA;gBACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAExB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAE1D,OAAO,KAAK,CAAA;IACd,CAAC;IAUD,GAAG,CACD,KAA4B,EAC5B,QAA2C,EAC3C,EAAe;QAEf,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,EAAE,GAAG,KAAmB,CAAA;YACxB,KAAK,GAAG,SAAS,CAAA;QACnB,CAAC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,MAAM,CAAA;QACnB,CAAC;QACD,IAAI,KAAK,KAAK,SAAS;YAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QACpD,IAAI,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;QAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QAErB,0DAA0D;QAC1D,6BAA6B;QAC7B,yDAAyD;QACzD,uDAAuD;QACvD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;QAC1D,OAAO,IAAI,CAAA;IACb,CAAC;IAED,+CAA+C;IAC/C,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,SAAS,CAAC;YAAE,OAAM;QAE3B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;QACxB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;aACjC,IAAI,IAAI,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;;YACrC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACzB,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,CAAA;IACxB,CAAC;IAED;;;OAGG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,CAAA;IACrB,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,KAAY;QACvB,IAAI,IAAI,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;;YACxC,IAAI,CAAC,YAAY,CAAC,IAAK,KAAiC,CAAC,MAAM,CAAA;QACpE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;IAED,CAAC,WAAW,CAAC;QACX,IAAI,IAAI,CAAC,UAAU,CAAC;YAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;;YAE3C,IAAI,CAAC,YAAY,CAAC,IAChB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CACf,CAAC,MAAM,CAAA;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,EAAW,CAAA;IACtC,CAAC;IAED,CAAC,KAAK,CAAC,CAAC,UAAmB,KAAK;QAC9B,GAAG,CAAC,CAAA,CAAC,QACH,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EACpB;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACxE,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,KAAY;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACH,IAAI,CAA8B,IAAO,EAAE,IAAkB;QAC3D,IAAI,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,IAAI,CAAA;QAChC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;QAEvB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAA;QAC/B,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QACjB,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAA;;YAC7D,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK,CAAA;QAClC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAA;QAErC,0CAA0C;QAC1C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,IAAI,CAAC,GAAG;gBAAE,IAAI,CAAC,GAAG,EAAE,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,kEAAkE;YAClE,gEAAgE;YAChE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CACd,CAAC,IAAI,CAAC,WAAW;gBACf,CAAC,CAAC,IAAI,IAAI,CAAQ,IAAuB,EAAE,IAAI,EAAE,IAAI,CAAC;gBACtD,CAAC,CAAC,IAAI,eAAe,CAAQ,IAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,CACpE,CAAA;YACD,IAAI,IAAI,CAAC,KAAK,CAAC;gBAAE,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;;gBACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACrB,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAA8B,IAAO;QACzC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,EAAE,CAAC;YACN,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;gBACvB,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;YAClB,CAAC;;gBAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YACpD,CAAC,CAAC,MAAM,EAAE,CAAA;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CACT,EAAS,EACT,OAAwC;QAExC,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,EAAE,CACA,EAAS,EACT,OAAwC;QAExC,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,CAClB,EAAqB,EACrB,OAA+B,CAChC,CAAA;QACD,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;YACvB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YAChB,CAAC;QACH,CAAC;aAAM,IAAI,EAAE,KAAK,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACxB,CAAC;aAAM,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACd,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAA;QAC7B,CAAC;aAAM,IAAI,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,GAAG,OAAyC,CAAA;YACnD,IAAI,IAAI,CAAC,KAAK,CAAC;gBAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;;gBAC1D,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;QACxC,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,EAAS,EACT,OAAwC;QAExC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,GAAG,CACD,EAAS,EACT,OAAwC;QAExC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CACnB,EAAqB,EACrB,OAA+B,CAChC,CAAA;QACD,iEAAiE;QACjE,kEAAkE;QAClE,wDAAwD;QACxD,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAA;YACnD,IACE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;gBACzB,CAAC,IAAI,CAAC,SAAS,CAAC;gBAChB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EACnB,CAAC;gBACD,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;YACvB,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;;;;;;OAOG;IACH,kBAAkB,CAA6B,EAAU;QACvD,MAAM,GAAG,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAiC,CAAC,CAAA;QACvE,IAAI,EAAE,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAA;YACvB,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,CAAA;IAC1B,CAAC;IAED,CAAC,cAAc,CAAC;QACd,IACE,CAAC,IAAI,CAAC,YAAY,CAAC;YACnB,CAAC,IAAI,CAAC,WAAW,CAAC;YAClB,CAAC,IAAI,CAAC,SAAS,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,EACT,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnB,IAAI,IAAI,CAAC,MAAM,CAAC;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;QAC5B,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,IAAI,CACF,EAAS,EACT,GAAG,IAAmB;QAEtB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACpB,kEAAkE;QAClE,IACE,EAAE,KAAK,OAAO;YACd,EAAE,KAAK,OAAO;YACd,EAAE,KAAK,SAAS;YAChB,IAAI,CAAC,SAAS,CAAC,EACf,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;aAAM,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;gBAC/B,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;oBACb,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAa,CAAC,CAAC,EAAE,IAAI,CAAC;oBACpD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAa,CAAC,CAAA;QACnC,CAAC;aAAM,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;QACxB,CAAC;aAAM,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;YACnB,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBAAE,OAAO,KAAK,CAAA;YACxD,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC/B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;YAChC,OAAO,GAAG,CAAA;QACZ,CAAC;aAAM,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAA;YAC1B,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YACvB,MAAM,GAAG,GACP,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM;gBAC7C,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;gBAC3B,CAAC,CAAC,KAAK,CAAA;YACX,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;YACtB,OAAO,GAAG,CAAA;QACZ,CAAC;aAAM,IAAI,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC3B,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAChC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;YACtB,OAAO,GAAG,CAAA;QACZ,CAAC;aAAM,IAAI,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,WAAW,EAAE,CAAC;YACjD,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1B,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAA;YAC3B,OAAO,GAAG,CAAA;QACZ,CAAC;QAED,2BAA2B;QAC3B,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAY,EAAE,GAAG,IAAI,CAAC,CAAA;QAC7C,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;QACtB,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,IAAW;QACpB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAa,CAAC,KAAK,KAAK;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAA;QACzD,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC9D,IAAI,CAAC,cAAc,CAAC,EAAE,CAAA;QACtB,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,CAAC,OAAO,CAAC;QACP,IAAI,IAAI,CAAC,WAAW,CAAC;YAAE,OAAO,KAAK,CAAA;QAEnC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;IACtB,CAAC;IAED,CAAC,QAAQ,CAAC;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAA;YAChC,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAa,CAAC,CAAA;gBAC7B,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBAAE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAChD,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,CAAC,CAAC,GAAG,EAAE,CAAA;QACT,CAAC;QACD,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9B,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACX,MAAM,GAAG,GAAqC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;YAC9D,UAAU,EAAE,CAAC;SACd,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAAE,GAAG,CAAC,UAAU,GAAG,CAAC,CAAA;QACzC,oDAAoD;QACpD,+BAA+B;QAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QACxB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;YAClB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACX,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACnB,GAAG,CAAC,UAAU,IAAK,CAA6B,CAAC,MAAM,CAAA;QAC3D,CAAC,CAAC,CAAA;QACF,MAAM,CAAC,CAAA;QACP,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;QAChD,CAAC;QACD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAChC,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC;YACZ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACd,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAe,EAAE,GAAG,CAAC,UAAU,CAAC,CAC1C,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAA;YAC/D,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YAClC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;QACjC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;OAIG;IACH,CAAC,MAAM,CAAC,aAAa,CAAC;QACpB,8DAA8D;QAC9D,cAAc;QACd,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;QACvB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,MAAM,IAAI,GAAG,KAAK,IAAyC,EAAE;YAC3D,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,OAAO,GAAG,IAAI,CAAA;YACd,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;QACzC,CAAC,CAAA;QACD,MAAM,IAAI,GAAG,GAAyC,EAAE;YACtD,IAAI,OAAO;gBAAE,OAAO,IAAI,EAAE,CAAA;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;YACvB,IAAI,GAAG,KAAK,IAAI;gBAAE,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAA;YAErE,IAAI,IAAI,CAAC,GAAG,CAAC;gBAAE,OAAO,IAAI,EAAE,CAAA;YAE5B,IAAI,OAA8C,CAAA;YAClD,IAAI,MAA8B,CAAA;YAClC,MAAM,KAAK,GAAG,CAAC,EAAW,EAAE,EAAE;gBAC5B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBACxB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBACtB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;gBAC9B,IAAI,EAAE,CAAA;gBACN,MAAM,CAAC,EAAE,CAAC,CAAA;YACZ,CAAC,CAAA;YACD,MAAM,MAAM,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;gBACxB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBACtB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;gBAC9B,IAAI,CAAC,KAAK,EAAE,CAAA;gBACZ,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACvC,CAAC,CAAA;YACD,MAAM,KAAK,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;gBACxB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBACxB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;gBAC9B,IAAI,EAAE,CAAA;gBACN,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAA;YAC3C,CAAC,CAAA;YACD,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAA;YAC5D,OAAO,IAAI,OAAO,CAAwB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrD,MAAM,GAAG,GAAG,CAAA;gBACZ,OAAO,GAAG,GAAG,CAAA;gBACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;gBAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;gBACzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAC3B,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,OAAO;YACL,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAA;YACb,CAAC;SACF,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,8DAA8D;QAC9D,cAAc;QACd,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;QACvB,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,MAAM,IAAI,GAAG,GAA+B,EAAE;YAC5C,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YACrB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;YACzB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YACrB,OAAO,GAAG,IAAI,CAAA;YACd,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAA;QACzC,CAAC,CAAA;QAED,MAAM,IAAI,GAAG,GAAgC,EAAE;YAC7C,IAAI,OAAO;gBAAE,OAAO,IAAI,EAAE,CAAA;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;YACzB,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;QACzD,CAAC,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAE1B,OAAO;YACL,IAAI;YACJ,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACf,OAAO,IAAI,CAAA;YACb,CAAC;SACF,CAAA;IACH,CAAC;IAED;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,EAAY;QAClB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACpB,IAAI,EAAE;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;QAEtB,sDAAsD;QACtD,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;QACvB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAEtB,MAAM,EAAE,GAAG,IAEV,CAAA;QACD,IAAI,OAAO,EAAE,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAAE,EAAE,CAAC,KAAK,EAAE,CAAA;QAE/D,IAAI,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAC9B,qDAAqD;;YAChD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAEzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,MAAM,KAAK,QAAQ;QACjB,OAAO,gBAAQ,CAAA;IACjB,CAAC;CACF;AAn/BD,4BAm/BC", "sourcesContent": ["const proc =\n  typeof process === 'object' && process\n    ? process\n    : {\n        stdout: null,\n        stderr: null,\n      }\nimport { EventEmitter } from 'node:events'\nimport Stream from 'node:stream'\nimport { StringDecoder } from 'node:string_decoder'\n\n/**\n * Same as StringDecoder, but exposing the `lastNeed` flag on the type\n */\ntype SD = StringDecoder & { lastNeed: boolean }\n\nexport type { SD, Pipe, PipeProxyErrors }\n\n/**\n * Return true if the argument is a Minipass stream, Node stream, or something\n * else that Minipass can interact with.\n */\nexport const isStream = (\n  s: any\n): s is Minipass.Readable | Minipass.Writable =>\n  !!s &&\n  typeof s === 'object' &&\n  (s instanceof Minipass ||\n    s instanceof Stream ||\n    isReadable(s) ||\n    isWritable(s))\n\n/**\n * Return true if the argument is a valid {@link Minipass.Readable}\n */\nexport const isReadable = (s: any): s is Minipass.Readable =>\n  !!s &&\n  typeof s === 'object' &&\n  s instanceof EventEmitter &&\n  typeof (s as Minipass.Readable).pipe === 'function' &&\n  // node core Writable streams have a pipe() method, but it throws\n  (s as Minipass.Readable).pipe !== Stream.Writable.prototype.pipe\n\n/**\n * Return true if the argument is a valid {@link Minipass.Writable}\n */\nexport const isWritable = (s: any): s is Minipass.Readable =>\n  !!s &&\n  typeof s === 'object' &&\n  s instanceof EventEmitter &&\n  typeof (s as Minipass.Writable).write === 'function' &&\n  typeof (s as Minipass.Writable).end === 'function'\n\nconst EOF = Symbol('EOF')\nconst MAYBE_EMIT_END = Symbol('maybeEmitEnd')\nconst EMITTED_END = Symbol('emittedEnd')\nconst EMITTING_END = Symbol('emittingEnd')\nconst EMITTED_ERROR = Symbol('emittedError')\nconst CLOSED = Symbol('closed')\nconst READ = Symbol('read')\nconst FLUSH = Symbol('flush')\nconst FLUSHCHUNK = Symbol('flushChunk')\nconst ENCODING = Symbol('encoding')\nconst DECODER = Symbol('decoder')\nconst FLOWING = Symbol('flowing')\nconst PAUSED = Symbol('paused')\nconst RESUME = Symbol('resume')\nconst BUFFER = Symbol('buffer')\nconst PIPES = Symbol('pipes')\nconst BUFFERLENGTH = Symbol('bufferLength')\nconst BUFFERPUSH = Symbol('bufferPush')\nconst BUFFERSHIFT = Symbol('bufferShift')\nconst OBJECTMODE = Symbol('objectMode')\n// internal event when stream is destroyed\nconst DESTROYED = Symbol('destroyed')\n// internal event when stream has an error\nconst ERROR = Symbol('error')\nconst EMITDATA = Symbol('emitData')\nconst EMITEND = Symbol('emitEnd')\nconst EMITEND2 = Symbol('emitEnd2')\nconst ASYNC = Symbol('async')\nconst ABORT = Symbol('abort')\nconst ABORTED = Symbol('aborted')\nconst SIGNAL = Symbol('signal')\nconst DATALISTENERS = Symbol('dataListeners')\nconst DISCARDED = Symbol('discarded')\n\nconst defer = (fn: (...a: any[]) => any) => Promise.resolve().then(fn)\nconst nodefer = (fn: (...a: any[]) => any) => fn()\n\n// events that mean 'the stream is over'\n// these are treated specially, and re-emitted\n// if they are listened for after emitting.\ntype EndishEvent = 'end' | 'finish' | 'prefinish'\nconst isEndish = (ev: any): ev is EndishEvent =>\n  ev === 'end' || ev === 'finish' || ev === 'prefinish'\n\nconst isArrayBufferLike = (b: any): b is ArrayBufferLike =>\n  b instanceof ArrayBuffer ||\n  (!!b &&\n    typeof b === 'object' &&\n    b.constructor &&\n    b.constructor.name === 'ArrayBuffer' &&\n    b.byteLength >= 0)\n\nconst isArrayBufferView = (b: any): b is ArrayBufferView =>\n  !Buffer.isBuffer(b) && ArrayBuffer.isView(b)\n\n/**\n * Options that may be passed to stream.pipe()\n */\nexport interface PipeOptions {\n  /**\n   * end the destination stream when the source stream ends\n   */\n  end?: boolean\n  /**\n   * proxy errors from the source stream to the destination stream\n   */\n  proxyErrors?: boolean\n}\n\n/**\n * Internal class representing a pipe to a destination stream.\n *\n * @internal\n */\nclass Pipe<T extends unknown> {\n  src: Minipass<T>\n  dest: Minipass<any, T>\n  opts: PipeOptions\n  ondrain: () => any\n  constructor(\n    src: Minipass<T>,\n    dest: Minipass.Writable,\n    opts: PipeOptions\n  ) {\n    this.src = src\n    this.dest = dest as Minipass<any, T>\n    this.opts = opts\n    this.ondrain = () => src[RESUME]()\n    this.dest.on('drain', this.ondrain)\n  }\n  unpipe() {\n    this.dest.removeListener('drain', this.ondrain)\n  }\n  // only here for the prototype\n  /* c8 ignore start */\n  proxyErrors(_er: any) {}\n  /* c8 ignore stop */\n  end() {\n    this.unpipe()\n    if (this.opts.end) this.dest.end()\n  }\n}\n\n/**\n * Internal class representing a pipe to a destination stream where\n * errors are proxied.\n *\n * @internal\n */\nclass PipeProxyErrors<T> extends Pipe<T> {\n  unpipe() {\n    this.src.removeListener('error', this.proxyErrors)\n    super.unpipe()\n  }\n  constructor(\n    src: Minipass<T>,\n    dest: Minipass.Writable,\n    opts: PipeOptions\n  ) {\n    super(src, dest, opts)\n    this.proxyErrors = er => dest.emit('error', er)\n    src.on('error', this.proxyErrors)\n  }\n}\n\nexport namespace Minipass {\n  /**\n   * Encoding used to create a stream that outputs strings rather than\n   * Buffer objects.\n   */\n  export type Encoding = BufferEncoding | 'buffer' | null\n\n  /**\n   * Any stream that Minipass can pipe into\n   */\n  export type Writable =\n    | Minipass<any, any, any>\n    | NodeJS.WriteStream\n    | (NodeJS.WriteStream & { fd: number })\n    | (EventEmitter & {\n        end(): any\n        write(chunk: any, ...args: any[]): any\n      })\n\n  /**\n   * Any stream that can be read from\n   */\n  export type Readable =\n    | Minipass<any, any, any>\n    | NodeJS.ReadStream\n    | (NodeJS.ReadStream & { fd: number })\n    | (EventEmitter & {\n        pause(): any\n        resume(): any\n        pipe(...destArgs: any[]): any\n      })\n\n  /**\n   * Utility type that can be iterated sync or async\n   */\n  export type DualIterable<T> = Iterable<T> & AsyncIterable<T>\n\n  type EventArguments = Record<string | symbol, unknown[]>\n\n  /**\n   * The listing of events that a Minipass class can emit.\n   * Extend this when extending the Minipass class, and pass as\n   * the third template argument.  The key is the name of the event,\n   * and the value is the argument list.\n   *\n   * Any undeclared events will still be allowed, but the handler will get\n   * arguments as `unknown[]`.\n   */\n  export interface Events<RType extends any = Buffer>\n    extends EventArguments {\n    readable: []\n    data: [chunk: RType]\n    error: [er: unknown]\n    abort: [reason: unknown]\n    drain: []\n    resume: []\n    end: []\n    finish: []\n    prefinish: []\n    close: []\n    [DESTROYED]: [er?: unknown]\n    [ERROR]: [er: unknown]\n  }\n\n  /**\n   * String or buffer-like data that can be joined and sliced\n   */\n  export type ContiguousData =\n    | Buffer\n    | ArrayBufferLike\n    | ArrayBufferView\n    | string\n  export type BufferOrString = Buffer | string\n\n  /**\n   * Options passed to the Minipass constructor.\n   */\n  export type SharedOptions = {\n    /**\n     * Defer all data emission and other events until the end of the\n     * current tick, similar to Node core streams\n     */\n    async?: boolean\n    /**\n     * A signal which will abort the stream\n     */\n    signal?: AbortSignal\n    /**\n     * Output string encoding. Set to `null` or `'buffer'` (or omit) to\n     * emit Buffer objects rather than strings.\n     *\n     * Conflicts with `objectMode`\n     */\n    encoding?: BufferEncoding | null | 'buffer'\n    /**\n     * Output data exactly as it was written, supporting non-buffer/string\n     * data (such as arbitrary objects, falsey values, etc.)\n     *\n     * Conflicts with `encoding`\n     */\n    objectMode?: boolean\n  }\n\n  /**\n   * Options for a string encoded output\n   */\n  export type EncodingOptions = SharedOptions & {\n    encoding: BufferEncoding\n    objectMode?: false\n  }\n\n  /**\n   * Options for contiguous data buffer output\n   */\n  export type BufferOptions = SharedOptions & {\n    encoding?: null | 'buffer'\n    objectMode?: false\n  }\n\n  /**\n   * Options for objectMode arbitrary output\n   */\n  export type ObjectModeOptions = SharedOptions & {\n    objectMode: true\n    encoding?: null\n  }\n\n  /**\n   * Utility type to determine allowed options based on read type\n   */\n  export type Options<T> =\n    | ObjectModeOptions\n    | (T extends string\n        ? EncodingOptions\n        : T extends Buffer\n        ? BufferOptions\n        : SharedOptions)\n}\n\nconst isObjectModeOptions = (\n  o: Minipass.SharedOptions\n): o is Minipass.ObjectModeOptions => !!o.objectMode\n\nconst isEncodingOptions = (\n  o: Minipass.SharedOptions\n): o is Minipass.EncodingOptions =>\n  !o.objectMode && !!o.encoding && o.encoding !== 'buffer'\n\n/**\n * Main export, the Minipass class\n *\n * `RType` is the type of data emitted, defaults to Buffer\n *\n * `WType` is the type of data to be written, if RType is buffer or string,\n * then any {@link Minipass.ContiguousData} is allowed.\n *\n * `Events` is the set of event handler signatures that this object\n * will emit, see {@link Minipass.Events}\n */\nexport class Minipass<\n    RType extends unknown = Buffer,\n    WType extends unknown = RType extends Minipass.BufferOrString\n      ? Minipass.ContiguousData\n      : RType,\n    Events extends Minipass.Events<RType> = Minipass.Events<RType>\n  >\n  extends EventEmitter\n  implements Minipass.DualIterable<RType>\n{\n  [FLOWING]: boolean = false;\n  [PAUSED]: boolean = false;\n  [PIPES]: Pipe<RType>[] = [];\n  [BUFFER]: RType[] = [];\n  [OBJECTMODE]: boolean;\n  [ENCODING]: BufferEncoding | null;\n  [ASYNC]: boolean;\n  [DECODER]: SD | null;\n  [EOF]: boolean = false;\n  [EMITTED_END]: boolean = false;\n  [EMITTING_END]: boolean = false;\n  [CLOSED]: boolean = false;\n  [EMITTED_ERROR]: unknown = null;\n  [BUFFERLENGTH]: number = 0;\n  [DESTROYED]: boolean = false;\n  [SIGNAL]?: AbortSignal;\n  [ABORTED]: boolean = false;\n  [DATALISTENERS]: number = 0;\n  [DISCARDED]: boolean = false\n\n  /**\n   * true if the stream can be written\n   */\n  writable: boolean = true\n  /**\n   * true if the stream can be read\n   */\n  readable: boolean = true\n\n  /**\n   * If `RType` is Buffer, then options do not need to be provided.\n   * Otherwise, an options object must be provided to specify either\n   * {@link Minipass.SharedOptions.objectMode} or\n   * {@link Minipass.SharedOptions.encoding}, as appropriate.\n   */\n  constructor(\n    ...args:\n      | [Minipass.ObjectModeOptions]\n      | (RType extends Buffer\n          ? [] | [Minipass.Options<RType>]\n          : [Minipass.Options<RType>])\n  ) {\n    const options: Minipass.Options<RType> = (args[0] ||\n      {}) as Minipass.Options<RType>\n    super()\n    if (options.objectMode && typeof options.encoding === 'string') {\n      throw new TypeError(\n        'Encoding and objectMode may not be used together'\n      )\n    }\n    if (isObjectModeOptions(options)) {\n      this[OBJECTMODE] = true\n      this[ENCODING] = null\n    } else if (isEncodingOptions(options)) {\n      this[ENCODING] = options.encoding\n      this[OBJECTMODE] = false\n    } else {\n      this[OBJECTMODE] = false\n      this[ENCODING] = null\n    }\n    this[ASYNC] = !!options.async\n    this[DECODER] = this[ENCODING]\n      ? (new StringDecoder(this[ENCODING]) as SD)\n      : null\n\n    //@ts-ignore - private option for debugging and testing\n    if (options && options.debugExposeBuffer === true) {\n      Object.defineProperty(this, 'buffer', { get: () => this[BUFFER] })\n    }\n    //@ts-ignore - private option for debugging and testing\n    if (options && options.debugExposePipes === true) {\n      Object.defineProperty(this, 'pipes', { get: () => this[PIPES] })\n    }\n\n    const { signal } = options\n    if (signal) {\n      this[SIGNAL] = signal\n      if (signal.aborted) {\n        this[ABORT]()\n      } else {\n        signal.addEventListener('abort', () => this[ABORT]())\n      }\n    }\n  }\n\n  /**\n   * The amount of data stored in the buffer waiting to be read.\n   *\n   * For Buffer strings, this will be the total byte length.\n   * For string encoding streams, this will be the string character length,\n   * according to JavaScript's `string.length` logic.\n   * For objectMode streams, this is a count of the items waiting to be\n   * emitted.\n   */\n  get bufferLength() {\n    return this[BUFFERLENGTH]\n  }\n\n  /**\n   * The `BufferEncoding` currently in use, or `null`\n   */\n  get encoding() {\n    return this[ENCODING]\n  }\n\n  /**\n   * @deprecated - This is a read only property\n   */\n  set encoding(_enc) {\n    throw new Error('Encoding must be set at instantiation time')\n  }\n\n  /**\n   * @deprecated - Encoding may only be set at instantiation time\n   */\n  setEncoding(_enc: Minipass.Encoding) {\n    throw new Error('Encoding must be set at instantiation time')\n  }\n\n  /**\n   * True if this is an objectMode stream\n   */\n  get objectMode() {\n    return this[OBJECTMODE]\n  }\n\n  /**\n   * @deprecated - This is a read-only property\n   */\n  set objectMode(_om) {\n    throw new Error('objectMode must be set at instantiation time')\n  }\n\n  /**\n   * true if this is an async stream\n   */\n  get ['async'](): boolean {\n    return this[ASYNC]\n  }\n  /**\n   * Set to true to make this stream async.\n   *\n   * Once set, it cannot be unset, as this would potentially cause incorrect\n   * behavior.  Ie, a sync stream can be made async, but an async stream\n   * cannot be safely made sync.\n   */\n  set ['async'](a: boolean) {\n    this[ASYNC] = this[ASYNC] || !!a\n  }\n\n  // drop everything and get out of the flow completely\n  [ABORT]() {\n    this[ABORTED] = true\n    this.emit('abort', this[SIGNAL]?.reason)\n    this.destroy(this[SIGNAL]?.reason)\n  }\n\n  /**\n   * True if the stream has been aborted.\n   */\n  get aborted() {\n    return this[ABORTED]\n  }\n  /**\n   * No-op setter. Stream aborted status is set via the AbortSignal provided\n   * in the constructor options.\n   */\n  set aborted(_) {}\n\n  /**\n   * Write data into the stream\n   *\n   * If the chunk written is a string, and encoding is not specified, then\n   * `utf8` will be assumed. If the stream encoding matches the encoding of\n   * a written string, and the state of the string decoder allows it, then\n   * the string will be passed through to either the output or the internal\n   * buffer without any processing. Otherwise, it will be turned into a\n   * Buffer object for processing into the desired encoding.\n   *\n   * If provided, `cb` function is called immediately before return for\n   * sync streams, or on next tick for async streams, because for this\n   * base class, a chunk is considered \"processed\" once it is accepted\n   * and either emitted or buffered. That is, the callback does not indicate\n   * that the chunk has been eventually emitted, though of course child\n   * classes can override this function to do whatever processing is required\n   * and call `super.write(...)` only once processing is completed.\n   */\n  write(chunk: WType, cb?: () => void): boolean\n  write(\n    chunk: WType,\n    encoding?: Minipass.Encoding,\n    cb?: () => void\n  ): boolean\n  write(\n    chunk: WType,\n    encoding?: Minipass.Encoding | (() => void),\n    cb?: () => void\n  ): boolean {\n    if (this[ABORTED]) return false\n    if (this[EOF]) throw new Error('write after end')\n\n    if (this[DESTROYED]) {\n      this.emit(\n        'error',\n        Object.assign(\n          new Error('Cannot call write after a stream was destroyed'),\n          { code: 'ERR_STREAM_DESTROYED' }\n        )\n      )\n      return true\n    }\n\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = 'utf8'\n    }\n\n    if (!encoding) encoding = 'utf8'\n\n    const fn = this[ASYNC] ? defer : nodefer\n\n    // convert array buffers and typed array views into buffers\n    // at some point in the future, we may want to do the opposite!\n    // leave strings and buffers as-is\n    // anything is only allowed if in object mode, so throw\n    if (!this[OBJECTMODE] && !Buffer.isBuffer(chunk)) {\n      if (isArrayBufferView(chunk)) {\n        //@ts-ignore - sinful unsafe type changing\n        chunk = Buffer.from(\n          chunk.buffer,\n          chunk.byteOffset,\n          chunk.byteLength\n        )\n      } else if (isArrayBufferLike(chunk)) {\n        //@ts-ignore - sinful unsafe type changing\n        chunk = Buffer.from(chunk)\n      } else if (typeof chunk !== 'string') {\n        throw new Error(\n          'Non-contiguous data written to non-objectMode stream'\n        )\n      }\n    }\n\n    // handle object mode up front, since it's simpler\n    // this yields better performance, fewer checks later.\n    if (this[OBJECTMODE]) {\n      // maybe impossible?\n      /* c8 ignore start */\n      if (this[FLOWING] && this[BUFFERLENGTH] !== 0) this[FLUSH](true)\n      /* c8 ignore stop */\n\n      if (this[FLOWING]) this.emit('data', chunk as unknown as RType)\n      else this[BUFFERPUSH](chunk as unknown as RType)\n\n      if (this[BUFFERLENGTH] !== 0) this.emit('readable')\n\n      if (cb) fn(cb)\n\n      return this[FLOWING]\n    }\n\n    // at this point the chunk is a buffer or string\n    // don't buffer it up or send it to the decoder\n    if (!(chunk as Minipass.BufferOrString).length) {\n      if (this[BUFFERLENGTH] !== 0) this.emit('readable')\n      if (cb) fn(cb)\n      return this[FLOWING]\n    }\n\n    // fast-path writing strings of same encoding to a stream with\n    // an empty buffer, skipping the buffer/decoder dance\n    if (\n      typeof chunk === 'string' &&\n      // unless it is a string already ready for us to use\n      !(encoding === this[ENCODING] && !this[DECODER]?.lastNeed)\n    ) {\n      //@ts-ignore - sinful unsafe type change\n      chunk = Buffer.from(chunk, encoding)\n    }\n\n    if (Buffer.isBuffer(chunk) && this[ENCODING]) {\n      //@ts-ignore - sinful unsafe type change\n      chunk = this[DECODER].write(chunk)\n    }\n\n    // Note: flushing CAN potentially switch us into not-flowing mode\n    if (this[FLOWING] && this[BUFFERLENGTH] !== 0) this[FLUSH](true)\n\n    if (this[FLOWING]) this.emit('data', chunk as unknown as RType)\n    else this[BUFFERPUSH](chunk as unknown as RType)\n\n    if (this[BUFFERLENGTH] !== 0) this.emit('readable')\n\n    if (cb) fn(cb)\n\n    return this[FLOWING]\n  }\n\n  /**\n   * Low-level explicit read method.\n   *\n   * In objectMode, the argument is ignored, and one item is returned if\n   * available.\n   *\n   * `n` is the number of bytes (or in the case of encoding streams,\n   * characters) to consume. If `n` is not provided, then the entire buffer\n   * is returned, or `null` is returned if no data is available.\n   *\n   * If `n` is greater that the amount of data in the internal buffer,\n   * then `null` is returned.\n   */\n  read(n?: number | null): RType | null {\n    if (this[DESTROYED]) return null\n    this[DISCARDED] = false\n\n    if (\n      this[BUFFERLENGTH] === 0 ||\n      n === 0 ||\n      (n && n > this[BUFFERLENGTH])\n    ) {\n      this[MAYBE_EMIT_END]()\n      return null\n    }\n\n    if (this[OBJECTMODE]) n = null\n\n    if (this[BUFFER].length > 1 && !this[OBJECTMODE]) {\n      // not object mode, so if we have an encoding, then RType is string\n      // otherwise, must be Buffer\n      this[BUFFER] = [\n        (this[ENCODING]\n          ? this[BUFFER].join('')\n          : Buffer.concat(\n              this[BUFFER] as Buffer[],\n              this[BUFFERLENGTH]\n            )) as RType,\n      ]\n    }\n\n    const ret = this[READ](n || null, this[BUFFER][0] as RType)\n    this[MAYBE_EMIT_END]()\n    return ret\n  }\n\n  [READ](n: number | null, chunk: RType) {\n    if (this[OBJECTMODE]) this[BUFFERSHIFT]()\n    else {\n      const c = chunk as Minipass.BufferOrString\n      if (n === c.length || n === null) this[BUFFERSHIFT]()\n      else if (typeof c === 'string') {\n        this[BUFFER][0] = c.slice(n) as RType\n        chunk = c.slice(0, n) as RType\n        this[BUFFERLENGTH] -= n\n      } else {\n        this[BUFFER][0] = c.subarray(n) as RType\n        chunk = c.subarray(0, n) as RType\n        this[BUFFERLENGTH] -= n\n      }\n    }\n\n    this.emit('data', chunk)\n\n    if (!this[BUFFER].length && !this[EOF]) this.emit('drain')\n\n    return chunk\n  }\n\n  /**\n   * End the stream, optionally providing a final write.\n   *\n   * See {@link Minipass#write} for argument descriptions\n   */\n  end(cb?: () => void): this\n  end(chunk: WType, cb?: () => void): this\n  end(chunk: WType, encoding?: Minipass.Encoding, cb?: () => void): this\n  end(\n    chunk?: WType | (() => void),\n    encoding?: Minipass.Encoding | (() => void),\n    cb?: () => void\n  ): this {\n    if (typeof chunk === 'function') {\n      cb = chunk as () => void\n      chunk = undefined\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = 'utf8'\n    }\n    if (chunk !== undefined) this.write(chunk, encoding)\n    if (cb) this.once('end', cb)\n    this[EOF] = true\n    this.writable = false\n\n    // if we haven't written anything, then go ahead and emit,\n    // even if we're not reading.\n    // we'll re-emit if a new 'end' listener is added anyway.\n    // This makes MP more suitable to write-only use cases.\n    if (this[FLOWING] || !this[PAUSED]) this[MAYBE_EMIT_END]()\n    return this\n  }\n\n  // don't let the internal resume be overwritten\n  [RESUME]() {\n    if (this[DESTROYED]) return\n\n    if (!this[DATALISTENERS] && !this[PIPES].length) {\n      this[DISCARDED] = true\n    }\n    this[PAUSED] = false\n    this[FLOWING] = true\n    this.emit('resume')\n    if (this[BUFFER].length) this[FLUSH]()\n    else if (this[EOF]) this[MAYBE_EMIT_END]()\n    else this.emit('drain')\n  }\n\n  /**\n   * Resume the stream if it is currently in a paused state\n   *\n   * If called when there are no pipe destinations or `data` event listeners,\n   * this will place the stream in a \"discarded\" state, where all data will\n   * be thrown away. The discarded state is removed if a pipe destination or\n   * data handler is added, if pause() is called, or if any synchronous or\n   * asynchronous iteration is started.\n   */\n  resume() {\n    return this[RESUME]()\n  }\n\n  /**\n   * Pause the stream\n   */\n  pause() {\n    this[FLOWING] = false\n    this[PAUSED] = true\n    this[DISCARDED] = false\n  }\n\n  /**\n   * true if the stream has been forcibly destroyed\n   */\n  get destroyed() {\n    return this[DESTROYED]\n  }\n\n  /**\n   * true if the stream is currently in a flowing state, meaning that\n   * any writes will be immediately emitted.\n   */\n  get flowing() {\n    return this[FLOWING]\n  }\n\n  /**\n   * true if the stream is currently in a paused state\n   */\n  get paused() {\n    return this[PAUSED]\n  }\n\n  [BUFFERPUSH](chunk: RType) {\n    if (this[OBJECTMODE]) this[BUFFERLENGTH] += 1\n    else this[BUFFERLENGTH] += (chunk as Minipass.BufferOrString).length\n    this[BUFFER].push(chunk)\n  }\n\n  [BUFFERSHIFT](): RType {\n    if (this[OBJECTMODE]) this[BUFFERLENGTH] -= 1\n    else\n      this[BUFFERLENGTH] -= (\n        this[BUFFER][0] as Minipass.BufferOrString\n      ).length\n    return this[BUFFER].shift() as RType\n  }\n\n  [FLUSH](noDrain: boolean = false) {\n    do {} while (\n      this[FLUSHCHUNK](this[BUFFERSHIFT]()) &&\n      this[BUFFER].length\n    )\n\n    if (!noDrain && !this[BUFFER].length && !this[EOF]) this.emit('drain')\n  }\n\n  [FLUSHCHUNK](chunk: RType) {\n    this.emit('data', chunk)\n    return this[FLOWING]\n  }\n\n  /**\n   * Pipe all data emitted by this stream into the destination provided.\n   *\n   * Triggers the flow of data.\n   */\n  pipe<W extends Minipass.Writable>(dest: W, opts?: PipeOptions): W {\n    if (this[DESTROYED]) return dest\n    this[DISCARDED] = false\n\n    const ended = this[EMITTED_END]\n    opts = opts || {}\n    if (dest === proc.stdout || dest === proc.stderr) opts.end = false\n    else opts.end = opts.end !== false\n    opts.proxyErrors = !!opts.proxyErrors\n\n    // piping an ended stream ends immediately\n    if (ended) {\n      if (opts.end) dest.end()\n    } else {\n      // \"as\" here just ignores the WType, which pipes don't care about,\n      // since they're only consuming from us, and writing to the dest\n      this[PIPES].push(\n        !opts.proxyErrors\n          ? new Pipe<RType>(this as Minipass<RType>, dest, opts)\n          : new PipeProxyErrors<RType>(this as Minipass<RType>, dest, opts)\n      )\n      if (this[ASYNC]) defer(() => this[RESUME]())\n      else this[RESUME]()\n    }\n\n    return dest\n  }\n\n  /**\n   * Fully unhook a piped destination stream.\n   *\n   * If the destination stream was the only consumer of this stream (ie,\n   * there are no other piped destinations or `'data'` event listeners)\n   * then the flow of data will stop until there is another consumer or\n   * {@link Minipass#resume} is explicitly called.\n   */\n  unpipe<W extends Minipass.Writable>(dest: W) {\n    const p = this[PIPES].find(p => p.dest === dest)\n    if (p) {\n      if (this[PIPES].length === 1) {\n        if (this[FLOWING] && this[DATALISTENERS] === 0) {\n          this[FLOWING] = false\n        }\n        this[PIPES] = []\n      } else this[PIPES].splice(this[PIPES].indexOf(p), 1)\n      p.unpipe()\n    }\n  }\n\n  /**\n   * Alias for {@link Minipass#on}\n   */\n  addListener<Event extends keyof Events>(\n    ev: Event,\n    handler: (...args: Events[Event]) => any\n  ): this {\n    return this.on(ev, handler)\n  }\n\n  /**\n   * Mostly identical to `EventEmitter.on`, with the following\n   * behavior differences to prevent data loss and unnecessary hangs:\n   *\n   * - Adding a 'data' event handler will trigger the flow of data\n   *\n   * - Adding a 'readable' event handler when there is data waiting to be read\n   *   will cause 'readable' to be emitted immediately.\n   *\n   * - Adding an 'endish' event handler ('end', 'finish', etc.) which has\n   *   already passed will cause the event to be emitted immediately and all\n   *   handlers removed.\n   *\n   * - Adding an 'error' event handler after an error has been emitted will\n   *   cause the event to be re-emitted immediately with the error previously\n   *   raised.\n   */\n  on<Event extends keyof Events>(\n    ev: Event,\n    handler: (...args: Events[Event]) => any\n  ): this {\n    const ret = super.on(\n      ev as string | symbol,\n      handler as (...a: any[]) => any\n    )\n    if (ev === 'data') {\n      this[DISCARDED] = false\n      this[DATALISTENERS]++\n      if (!this[PIPES].length && !this[FLOWING]) {\n        this[RESUME]()\n      }\n    } else if (ev === 'readable' && this[BUFFERLENGTH] !== 0) {\n      super.emit('readable')\n    } else if (isEndish(ev) && this[EMITTED_END]) {\n      super.emit(ev)\n      this.removeAllListeners(ev)\n    } else if (ev === 'error' && this[EMITTED_ERROR]) {\n      const h = handler as (...a: Events['error']) => any\n      if (this[ASYNC]) defer(() => h.call(this, this[EMITTED_ERROR]))\n      else h.call(this, this[EMITTED_ERROR])\n    }\n    return ret\n  }\n\n  /**\n   * Alias for {@link Minipass#off}\n   */\n  removeListener<Event extends keyof Events>(\n    ev: Event,\n    handler: (...args: Events[Event]) => any\n  ) {\n    return this.off(ev, handler)\n  }\n\n  /**\n   * Mostly identical to `EventEmitter.off`\n   *\n   * If a 'data' event handler is removed, and it was the last consumer\n   * (ie, there are no pipe destinations or other 'data' event listeners),\n   * then the flow of data will stop until there is another consumer or\n   * {@link Minipass#resume} is explicitly called.\n   */\n  off<Event extends keyof Events>(\n    ev: Event,\n    handler: (...args: Events[Event]) => any\n  ) {\n    const ret = super.off(\n      ev as string | symbol,\n      handler as (...a: any[]) => any\n    )\n    // if we previously had listeners, and now we don't, and we don't\n    // have any pipes, then stop the flow, unless it's been explicitly\n    // put in a discarded flowing state via stream.resume().\n    if (ev === 'data') {\n      this[DATALISTENERS] = this.listeners('data').length\n      if (\n        this[DATALISTENERS] === 0 &&\n        !this[DISCARDED] &&\n        !this[PIPES].length\n      ) {\n        this[FLOWING] = false\n      }\n    }\n    return ret\n  }\n\n  /**\n   * Mostly identical to `EventEmitter.removeAllListeners`\n   *\n   * If all 'data' event handlers are removed, and they were the last consumer\n   * (ie, there are no pipe destinations), then the flow of data will stop\n   * until there is another consumer or {@link Minipass#resume} is explicitly\n   * called.\n   */\n  removeAllListeners<Event extends keyof Events>(ev?: Event) {\n    const ret = super.removeAllListeners(ev as string | symbol | undefined)\n    if (ev === 'data' || ev === undefined) {\n      this[DATALISTENERS] = 0\n      if (!this[DISCARDED] && !this[PIPES].length) {\n        this[FLOWING] = false\n      }\n    }\n    return ret\n  }\n\n  /**\n   * true if the 'end' event has been emitted\n   */\n  get emittedEnd() {\n    return this[EMITTED_END]\n  }\n\n  [MAYBE_EMIT_END]() {\n    if (\n      !this[EMITTING_END] &&\n      !this[EMITTED_END] &&\n      !this[DESTROYED] &&\n      this[BUFFER].length === 0 &&\n      this[EOF]\n    ) {\n      this[EMITTING_END] = true\n      this.emit('end')\n      this.emit('prefinish')\n      this.emit('finish')\n      if (this[CLOSED]) this.emit('close')\n      this[EMITTING_END] = false\n    }\n  }\n\n  /**\n   * Mostly identical to `EventEmitter.emit`, with the following\n   * behavior differences to prevent data loss and unnecessary hangs:\n   *\n   * If the stream has been destroyed, and the event is something other\n   * than 'close' or 'error', then `false` is returned and no handlers\n   * are called.\n   *\n   * If the event is 'end', and has already been emitted, then the event\n   * is ignored. If the stream is in a paused or non-flowing state, then\n   * the event will be deferred until data flow resumes. If the stream is\n   * async, then handlers will be called on the next tick rather than\n   * immediately.\n   *\n   * If the event is 'close', and 'end' has not yet been emitted, then\n   * the event will be deferred until after 'end' is emitted.\n   *\n   * If the event is 'error', and an AbortSignal was provided for the stream,\n   * and there are no listeners, then the event is ignored, matching the\n   * behavior of node core streams in the presense of an AbortSignal.\n   *\n   * If the event is 'finish' or 'prefinish', then all listeners will be\n   * removed after emitting the event, to prevent double-firing.\n   */\n  emit<Event extends keyof Events>(\n    ev: Event,\n    ...args: Events[Event]\n  ): boolean {\n    const data = args[0]\n    // error and close are only events allowed after calling destroy()\n    if (\n      ev !== 'error' &&\n      ev !== 'close' &&\n      ev !== DESTROYED &&\n      this[DESTROYED]\n    ) {\n      return false\n    } else if (ev === 'data') {\n      return !this[OBJECTMODE] && !data\n        ? false\n        : this[ASYNC]\n        ? (defer(() => this[EMITDATA](data as RType)), true)\n        : this[EMITDATA](data as RType)\n    } else if (ev === 'end') {\n      return this[EMITEND]()\n    } else if (ev === 'close') {\n      this[CLOSED] = true\n      // don't emit close before 'end' and 'finish'\n      if (!this[EMITTED_END] && !this[DESTROYED]) return false\n      const ret = super.emit('close')\n      this.removeAllListeners('close')\n      return ret\n    } else if (ev === 'error') {\n      this[EMITTED_ERROR] = data\n      super.emit(ERROR, data)\n      const ret =\n        !this[SIGNAL] || this.listeners('error').length\n          ? super.emit('error', data)\n          : false\n      this[MAYBE_EMIT_END]()\n      return ret\n    } else if (ev === 'resume') {\n      const ret = super.emit('resume')\n      this[MAYBE_EMIT_END]()\n      return ret\n    } else if (ev === 'finish' || ev === 'prefinish') {\n      const ret = super.emit(ev)\n      this.removeAllListeners(ev)\n      return ret\n    }\n\n    // Some other unknown event\n    const ret = super.emit(ev as string, ...args)\n    this[MAYBE_EMIT_END]()\n    return ret\n  }\n\n  [EMITDATA](data: RType) {\n    for (const p of this[PIPES]) {\n      if (p.dest.write(data as RType) === false) this.pause()\n    }\n    const ret = this[DISCARDED] ? false : super.emit('data', data)\n    this[MAYBE_EMIT_END]()\n    return ret\n  }\n\n  [EMITEND]() {\n    if (this[EMITTED_END]) return false\n\n    this[EMITTED_END] = true\n    this.readable = false\n    return this[ASYNC]\n      ? (defer(() => this[EMITEND2]()), true)\n      : this[EMITEND2]()\n  }\n\n  [EMITEND2]() {\n    if (this[DECODER]) {\n      const data = this[DECODER].end()\n      if (data) {\n        for (const p of this[PIPES]) {\n          p.dest.write(data as RType)\n        }\n        if (!this[DISCARDED]) super.emit('data', data)\n      }\n    }\n\n    for (const p of this[PIPES]) {\n      p.end()\n    }\n    const ret = super.emit('end')\n    this.removeAllListeners('end')\n    return ret\n  }\n\n  /**\n   * Return a Promise that resolves to an array of all emitted data once\n   * the stream ends.\n   */\n  async collect(): Promise<RType[] & { dataLength: number }> {\n    const buf: RType[] & { dataLength: number } = Object.assign([], {\n      dataLength: 0,\n    })\n    if (!this[OBJECTMODE]) buf.dataLength = 0\n    // set the promise first, in case an error is raised\n    // by triggering the flow here.\n    const p = this.promise()\n    this.on('data', c => {\n      buf.push(c)\n      if (!this[OBJECTMODE])\n        buf.dataLength += (c as Minipass.BufferOrString).length\n    })\n    await p\n    return buf\n  }\n\n  /**\n   * Return a Promise that resolves to the concatenation of all emitted data\n   * once the stream ends.\n   *\n   * Not allowed on objectMode streams.\n   */\n  async concat(): Promise<RType> {\n    if (this[OBJECTMODE]) {\n      throw new Error('cannot concat in objectMode')\n    }\n    const buf = await this.collect()\n    return (\n      this[ENCODING]\n        ? buf.join('')\n        : Buffer.concat(buf as Buffer[], buf.dataLength)\n    ) as RType\n  }\n\n  /**\n   * Return a void Promise that resolves once the stream ends.\n   */\n  async promise(): Promise<void> {\n    return new Promise<void>((resolve, reject) => {\n      this.on(DESTROYED, () => reject(new Error('stream destroyed')))\n      this.on('error', er => reject(er))\n      this.on('end', () => resolve())\n    })\n  }\n\n  /**\n   * Asynchronous `for await of` iteration.\n   *\n   * This will continue emitting all chunks until the stream terminates.\n   */\n  [Symbol.asyncIterator](): AsyncGenerator<RType, void, void> {\n    // set this up front, in case the consumer doesn't call next()\n    // right away.\n    this[DISCARDED] = false\n    let stopped = false\n    const stop = async (): Promise<IteratorReturnResult<void>> => {\n      this.pause()\n      stopped = true\n      return { value: undefined, done: true }\n    }\n    const next = (): Promise<IteratorResult<RType, void>> => {\n      if (stopped) return stop()\n      const res = this.read()\n      if (res !== null) return Promise.resolve({ done: false, value: res })\n\n      if (this[EOF]) return stop()\n\n      let resolve!: (res: IteratorResult<RType>) => void\n      let reject!: (er: unknown) => void\n      const onerr = (er: unknown) => {\n        this.off('data', ondata)\n        this.off('end', onend)\n        this.off(DESTROYED, ondestroy)\n        stop()\n        reject(er)\n      }\n      const ondata = (value: RType) => {\n        this.off('error', onerr)\n        this.off('end', onend)\n        this.off(DESTROYED, ondestroy)\n        this.pause()\n        resolve({ value, done: !!this[EOF] })\n      }\n      const onend = () => {\n        this.off('error', onerr)\n        this.off('data', ondata)\n        this.off(DESTROYED, ondestroy)\n        stop()\n        resolve({ done: true, value: undefined })\n      }\n      const ondestroy = () => onerr(new Error('stream destroyed'))\n      return new Promise<IteratorResult<RType>>((res, rej) => {\n        reject = rej\n        resolve = res\n        this.once(DESTROYED, ondestroy)\n        this.once('error', onerr)\n        this.once('end', onend)\n        this.once('data', ondata)\n      })\n    }\n\n    return {\n      next,\n      throw: stop,\n      return: stop,\n      [Symbol.asyncIterator]() {\n        return this\n      },\n    }\n  }\n\n  /**\n   * Synchronous `for of` iteration.\n   *\n   * The iteration will terminate when the internal buffer runs out, even\n   * if the stream has not yet terminated.\n   */\n  [Symbol.iterator](): Generator<RType, void, void> {\n    // set this up front, in case the consumer doesn't call next()\n    // right away.\n    this[DISCARDED] = false\n    let stopped = false\n    const stop = (): IteratorReturnResult<void> => {\n      this.pause()\n      this.off(ERROR, stop)\n      this.off(DESTROYED, stop)\n      this.off('end', stop)\n      stopped = true\n      return { done: true, value: undefined }\n    }\n\n    const next = (): IteratorResult<RType, void> => {\n      if (stopped) return stop()\n      const value = this.read()\n      return value === null ? stop() : { done: false, value }\n    }\n\n    this.once('end', stop)\n    this.once(ERROR, stop)\n    this.once(DESTROYED, stop)\n\n    return {\n      next,\n      throw: stop,\n      return: stop,\n      [Symbol.iterator]() {\n        return this\n      },\n    }\n  }\n\n  /**\n   * Destroy a stream, preventing it from being used for any further purpose.\n   *\n   * If the stream has a `close()` method, then it will be called on\n   * destruction.\n   *\n   * After destruction, any attempt to write data, read data, or emit most\n   * events will be ignored.\n   *\n   * If an error argument is provided, then it will be emitted in an\n   * 'error' event.\n   */\n  destroy(er?: unknown) {\n    if (this[DESTROYED]) {\n      if (er) this.emit('error', er)\n      else this.emit(DESTROYED)\n      return this\n    }\n\n    this[DESTROYED] = true\n    this[DISCARDED] = true\n\n    // throw away all buffered data, it's never coming out\n    this[BUFFER].length = 0\n    this[BUFFERLENGTH] = 0\n\n    const wc = this as Minipass<RType, WType, Events> & {\n      close?: () => void\n    }\n    if (typeof wc.close === 'function' && !this[CLOSED]) wc.close()\n\n    if (er) this.emit('error', er)\n    // if no error to emit, still reject pending promises\n    else this.emit(DESTROYED)\n\n    return this\n  }\n\n  /**\n   * Alias for {@link isStream}\n   *\n   * Former export location, maintained for backwards compatibility.\n   *\n   * @deprecated\n   */\n  static get isStream() {\n    return isStream\n  }\n}\n"]}