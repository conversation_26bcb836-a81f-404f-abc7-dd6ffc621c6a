import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock,
  Send,
  MessageCircle,
  Globe,
  Users
} from 'lucide-react'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
    alert('Thank you for your message! We will get back to you soon.')
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    })
  }

  const contactInfo = [
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Our Address",
      details: [
        "Gauveda Global Pvt. Ltd.",
        "123 Rural Road, Village Name",
        "District, State - 123456",
        "India"
      ]
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone Numbers",
      details: [
        "+91 98765 43210",
        "+91 87654 32109",
        "Toll Free: 1800-123-4567"
      ]
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email Addresses",
      details: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ]
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Business Hours",
      details: [
        "Monday - Friday: 9:00 AM - 6:00 PM",
        "Saturday: 9:00 AM - 2:00 PM",
        "Sunday: Closed",
        "IST (Indian Standard Time)"
      ]
    }
  ]

  const departments = [
    {
      icon: <Users className="h-8 w-8" />,
      title: "Sales & Bulk Orders",
      description: "For bulk purchases, wholesale inquiries, and export orders",
      contact: "<EMAIL>",
      phone: "+91 98765 43210"
    },
    {
      icon: <MessageCircle className="h-8 w-8" />,
      title: "Customer Support",
      description: "For product information, order status, and general inquiries",
      contact: "<EMAIL>",
      phone: "+91 87654 32109"
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "International Trade",
      description: "For export documentation, shipping, and international partnerships",
      contact: "<EMAIL>",
      phone: "+91 76543 21098"
    }
  ]

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-cream py-20">
        <div className="container-custom">
          <motion.div
            className="text-center max-w-4xl mx-auto space-y-6"
            {...fadeInUp}
          >
            <h1 className="text-5xl lg:text-6xl font-heading font-bold text-primary-900">
              Contact Us
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed">
              Get in touch with our team for any inquiries, bulk orders, or 
              partnership opportunities. We're here to help you with all 
              your cow-based product needs.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Get In Touch
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              We're always ready to assist you. Choose the best way to reach us 
              based on your needs and location.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {contactInfo.map((info, index) => (
              <motion.div
                key={index}
                className="text-center space-y-4 p-6 rounded-xl bg-cream hover:bg-primary-50 transition-colors duration-300"
                variants={fadeInUp}
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-800 text-white rounded-full">
                  {info.icon}
                </div>
                <h3 className="text-lg font-heading font-semibold text-primary-900">
                  {info.title}
                </h3>
                <div className="space-y-1">
                  {info.details.map((detail, idx) => (
                    <p key={idx} className="text-gray-600 text-sm">
                      {detail}
                    </p>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Departments */}
      <section className="section-padding bg-cream">
        <div className="container-custom">
          <motion.div
            className="text-center mb-12"
            {...fadeInUp}
          >
            <h2 className="text-4xl font-heading font-bold text-primary-900 mb-4">
              Contact by Department
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Reach out to the right department for faster and more 
              specialized assistance with your specific needs.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {departments.map((dept, index) => (
              <motion.div
                key={index}
                className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                variants={fadeInUp}
              >
                <div className="flex items-center mb-4">
                  <div className="bg-primary-800 text-white p-3 rounded-full mr-4">
                    {dept.icon}
                  </div>
                  <h3 className="text-xl font-heading font-semibold text-primary-900">
                    {dept.title}
                  </h3>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {dept.description}
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-primary-600" />
                    <a 
                      href={`mailto:${dept.contact}`}
                      className="text-primary-800 hover:text-primary-600 transition-colors"
                    >
                      {dept.contact}
                    </a>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-primary-600" />
                    <a 
                      href={`tel:${dept.phone}`}
                      className="text-primary-800 hover:text-primary-600 transition-colors"
                    >
                      {dept.phone}
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Map */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div>
                <h2 className="text-3xl font-heading font-bold text-primary-900 mb-4">
                  Send us a Message
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  Fill out the form below and we'll get back to you as soon as possible. 
                  For urgent inquiries, please call us directly.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject *
                    </label>
                    <select
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="">Select Subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="bulk-order">Bulk Order</option>
                      <option value="product-info">Product Information</option>
                      <option value="partnership">Partnership</option>
                      <option value="support">Customer Support</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    placeholder="Please provide details about your inquiry..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full btn-primary flex items-center justify-center"
                >
                  <Send className="mr-2 h-5 w-5" />
                  Send Message
                </button>
              </form>
            </motion.div>

            {/* Map & Additional Info */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div>
                <h3 className="text-2xl font-heading font-bold text-primary-900 mb-4">
                  Visit Our Office
                </h3>
                <p className="text-gray-700 leading-relaxed mb-6">
                  We welcome visitors to our facility. Please schedule an appointment 
                  in advance to ensure our team is available to assist you.
                </p>
              </div>

              {/* Placeholder for Google Maps */}
              <div className="bg-gray-200 rounded-xl h-64 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <MapPin className="h-12 w-12 mx-auto mb-2" />
                  <p>Interactive Map</p>
                  <p className="text-sm">Google Maps integration would go here</p>
                </div>
              </div>

              <div className="bg-cream p-6 rounded-xl">
                <h4 className="font-heading font-semibold text-primary-900 mb-3">
                  Quick Response Guarantee
                </h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span>Email inquiries: Within 24 hours</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span>Phone calls: Immediate response during business hours</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span>Bulk order quotes: Within 48 hours</span>
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Contact
